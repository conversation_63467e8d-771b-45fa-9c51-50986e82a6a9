@using CarPointCMS.Models.ViewModels
@{
    ViewData["Title"] = "Edit Listing";
}

@model ListingEditViewModel

<h1 class="h3 mb-3 text-gray-800">Edit Listing</h1>

<form asp-area="Admin" asp-controller="Listing" asp-action="Edit" asp-route-id="@Model.Id" method="post" enctype="multipart/form-data">
    <input type="hidden" name="CurrentPhoto" value="@(Model.ExistingFeaturedPhoto ?? "")">

    <div class="card shadow mb-4 t-left">
        <div class="card-header py-3">
            <h6 class="m-0 mt-2 font-weight-bold text-primary"></h6>
            <div class="float-right d-inline">
                <a asp-area="Admin" asp-controller="Listing" asp-action="Index" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> View All</a>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-3">
                    <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                        <a class="nav-link active" id="p1_tab" data-toggle="pill" href="#p1" role="tab" aria-controls="p1" aria-selected="true">Main Section</a>
                        <a class="nav-link" id="p10_tab" data-toggle="pill" href="#p10" role="tab" aria-controls="p10" aria-selected="false">Features</a>
                        <a class="nav-link" id="p2_tab" data-toggle="pill" href="#p2" role="tab" aria-controls="p2" aria-selected="false">Opening Hour</a>
                        <a class="nav-link" id="p3_tab" data-toggle="pill" href="#p3" role="tab" aria-controls="p3" aria-selected="false">Social Media</a>
                        <a class="nav-link" id="p4_tab" data-toggle="pill" href="#p4" role="tab" aria-controls="p4" aria-selected="false">Amenity</a>
                        <a class="nav-link" id="p5_tab" data-toggle="pill" href="#p5" role="tab" aria-controls="p5" aria-selected="false">Photo Gallery</a>
                        <a class="nav-link" id="p6_tab" data-toggle="pill" href="#p6" role="tab" aria-controls="p6" aria-selected="false">Video Gallery</a>
                        <a class="nav-link" id="p7_tab" data-toggle="pill" href="#p7" role="tab" aria-controls="p7" aria-selected="false">Additional Features</a>
                        <a class="nav-link" id="p8_tab" data-toggle="pill" href="#p8" role="tab" aria-controls="p8" aria-selected="false">SEO</a>
                        <a class="nav-link" id="p9_tab" data-toggle="pill" href="#p9" role="tab" aria-controls="p9" aria-selected="false">Status and Featured</a>
                    </div>
                </div>
                <div class="col-9">
                    <div class="tab-content" id="v-pills-tabContent">

                        <!-- Tab 1: Main Section -->
                        <div class="tab-pane fade show active" id="p1" role="tabpanel" aria-labelledby="p1_tab">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Name *</label>
                                        <input type="text" name="ListingName" class="form-control" value="@(ViewBag.Listing?.ListingName ?? "")" autofocus>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Slug</label>
                                        <input type="text" name="ListingSlug" class="form-control" value="@(ViewBag.Listing?.ListingSlug ?? "")">
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="">Description *</label>
                                <textarea name="ListingDescription" class="form-control editor" cols="30" rows="10">@(ViewBag.Listing?.ListingDescription ?? "")</textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Listing Brand</label>
                                        <select name="ListingBrandId" class="form-control select2">
                                            @* TODO: Populate from listing brands when model is implemented *@
                                            <option value="">Select Brand</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Listing Location</label>
                                        <select name="ListingLocationId" class="form-control select2">
                                            @* TODO: Populate from listing locations when model is implemented *@
                                            <option value="">Select Location</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Address</label>
                                        <textarea name="ListingAddress" class="form-control h_70" cols="30" rows="10">@(ViewBag.Listing?.ListingAddress ?? "")</textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Phone</label>
                                        <textarea name="ListingPhone" class="form-control h_70" cols="30" rows="10">@(ViewBag.Listing?.ListingPhone ?? "")</textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Email</label>
                                        <textarea name="ListingEmail" class="form-control h_70" cols="30" rows="10">@(ViewBag.Listing?.ListingEmail ?? "")</textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Map Iframe Code</label>
                                        <textarea name="ListingMap" class="form-control h_70" cols="30" rows="10">@(ViewBag.Listing?.ListingMap ?? "")</textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="">Website</label>
                                <input type="text" name="ListingWebsite" class="form-control" value="@(ViewBag.Listing?.ListingWebsite ?? "")">
                            </div>

                            <div class="form-group">
                                <label for="">Existing Featured Photo *</label>
                                <div>
                                    <img src="~/uploads/listing_featured_photos/@(ViewBag.Listing?.ListingFeaturedPhoto ?? "default.jpg")" class="w_200" alt="">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="">Change Featured Photo *</label>
                                <div>
                                    <input type="file" name="ListingFeaturedPhoto">
                                </div>
                            </div>
                        </div>

                        <!-- Tab 10: Features -->
                        <div class="tab-pane fade" id="p10" role="tabpanel" aria-labelledby="p10_tab">
                            <h4 class="heading-in-tab">Features</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Price *</label>
                                        <input type="text" name="ListingPrice" class="form-control" value="@(ViewBag.Listing?.ListingPrice ?? "")">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Type</label>
                                        <select name="ListingType" class="form-control">
                                            <option value="New Car" selected="@(ViewBag.Listing?.ListingType == "New Car")">New Car</option>
                                            <option value="Used Car" selected="@(ViewBag.Listing?.ListingType == "Used Car")">Used Car</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Exterior Color</label>
                                        <input type="text" name="ListingExteriorColor" class="form-control" value="@(ViewBag.Listing?.ListingExteriorColor ?? "")">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Interior Color</label>
                                        <input type="text" name="ListingInteriorColor" class="form-control" value="@(ViewBag.Listing?.ListingInteriorColor ?? "")">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Cylinder</label>
                                        <input type="text" name="ListingCylinder" class="form-control" value="@(ViewBag.Listing?.ListingCylinder ?? "")">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Fuel Type</label>
                                        <input type="text" name="ListingFuelType" class="form-control" value="@(ViewBag.Listing?.ListingFuelType ?? "")">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Transmission</label>
                                        <input type="text" name="ListingTransmission" class="form-control" value="@(ViewBag.Listing?.ListingTransmission ?? "")">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Engine Capacity</label>
                                        <input type="text" name="ListingEngineCapacity" class="form-control" value="@(ViewBag.Listing?.ListingEngineCapacity ?? "")">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">VIN</label>
                                        <input type="text" name="ListingVin" class="form-control" value="@(ViewBag.Listing?.ListingVin ?? "")">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Body</label>
                                        <input type="text" name="ListingBody" class="form-control" value="@(ViewBag.Listing?.ListingBody ?? "")">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Seat</label>
                                        <input type="text" name="ListingSeat" class="form-control" value="@(ViewBag.Listing?.ListingSeat ?? "")">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Wheel</label>
                                        <input type="text" name="ListingWheel" class="form-control" value="@(ViewBag.Listing?.ListingWheel ?? "")">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Door</label>
                                        <input type="text" name="ListingDoor" class="form-control" value="@(ViewBag.Listing?.ListingDoor ?? "")">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Mileage</label>
                                        <input type="text" name="ListingMileage" class="form-control" value="@(ViewBag.Listing?.ListingMileage ?? "")">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Model Year</label>
                                        <input type="text" name="ListingModelYear" class="form-control" value="@(ViewBag.Listing?.ListingModelYear ?? "")">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tab 2: Opening Hours -->
                        <div class="tab-pane fade" id="p2" role="tabpanel" aria-labelledby="p2_tab">
                            <h4 class="heading-in-tab">Opening Hour</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Monday</label>
                                        <input type="text" name="ListingOhMonday" class="form-control" value="@(ViewBag.Listing?.ListingOhMonday ?? "")">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Tuesday</label>
                                        <input type="text" name="ListingOhTuesday" class="form-control" value="@(ViewBag.Listing?.ListingOhTuesday ?? "")">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Wednesday</label>
                                        <input type="text" name="ListingOhWednesday" class="form-control" value="@(ViewBag.Listing?.ListingOhWednesday ?? "")">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Thursday</label>
                                        <input type="text" name="ListingOhThursday" class="form-control" value="@(ViewBag.Listing?.ListingOhThursday ?? "")">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Friday</label>
                                        <input type="text" name="ListingOhFriday" class="form-control" value="@(ViewBag.Listing?.ListingOhFriday ?? "")">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Saturday</label>
                                        <input type="text" name="ListingOhSaturday" class="form-control" value="@(ViewBag.Listing?.ListingOhSaturday ?? "")">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Sunday</label>
                                        <input type="text" name="ListingOhSunday" class="form-control" value="@(ViewBag.Listing?.ListingOhSunday ?? "")">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tab 3: Social Media -->
                        <div class="tab-pane fade" id="p3" role="tabpanel" aria-labelledby="p3_tab">
                            <h4 class="heading-in-tab">Existing Social Media</h4>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            @* TODO: Populate existing social media items when model is implemented *@
                                            <tr>
                                                <td colspan="3">No existing social media items</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <h4 class="heading-in-tab mt_30">New Social Media</h4>
                            <div class="social_item">
                                <div class="row">
                                    <div class="col-md-5">
                                        <div class="form-group">
                                            <select name="SocialIcon[]" class="form-control">
                                                <option value="Facebook">Facebook</option>
                                                <option value="Twitter">Twitter</option>
                                                <option value="LinkedIn">LinkedIn</option>
                                                <option value="YouTube">YouTube</option>
                                                <option value="Pinterest">Pinterest</option>
                                                <option value="GooglePlus">Google Plus</option>
                                                <option value="Instagram">Instagram</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <input type="text" name="SocialUrl[]" class="form-control" placeholder="URL">
                                        </div>
                                    </div>
                                    <div class="col-md-1">
                                        <div class="btn btn-success add_social_more"><i class="fas fa-plus"></i></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tab 4: Amenity -->
                        <div class="tab-pane fade" id="p4" role="tabpanel" aria-labelledby="p4_tab">
                            <h4 class="heading-in-tab">Amenity</h4>
                            <div class="row">
                                @* TODO: Populate from amenities when model is implemented *@
                                <div class="col-md-4">
                                    <div class="form-check mb_10">
                                        <input class="form-check-input" name="Amenity[]" type="checkbox" value="1" id="amenities1">
                                        <label class="form-check-label" for="amenities1">
                                            Sample Amenity
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tab 5: Photo Gallery -->
                        <div class="tab-pane fade" id="p5" role="tabpanel" aria-labelledby="p5_tab">
                            <h4 class="heading-in-tab">Existing Photos</h4>
                            <div class="row">
                                @* TODO: Display existing photos when model is implemented *@
                                <div class="col-md-12">
                                    <p>No existing photos</p>
                                </div>
                            </div>

                            <h4 class="heading-in-tab mt_30">New Photos</h4>
                            <div class="photo_item">
                                <div class="row">
                                    <div class="col-md-5">
                                        <div class="form-group">
                                            <div>
                                                <input type="file" name="PhotoList[]">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-1">
                                        <div class="btn btn-success add_photo_more"><i class="fas fa-plus"></i></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tab 6: Video Gallery -->
                        <div class="tab-pane fade" id="p6" role="tabpanel" aria-labelledby="p6_tab">
                            <h4 class="heading-in-tab">Existing Videos</h4>
                            <div class="row">
                                @* TODO: Display existing videos when model is implemented *@
                                <div class="col-md-12">
                                    <p>No existing videos</p>
                                </div>
                            </div>

                            <h4 class="heading-in-tab mt_30">New Videos</h4>
                            <div class="video_item">
                                <div class="row">
                                    <div class="col-md-5">
                                        <div class="form-group">
                                            <input type="text" name="YoutubeVideoId[]" class="form-control" placeholder="YouTube Video ID">
                                        </div>
                                    </div>
                                    <div class="col-md-1">
                                        <div class="btn btn-success add_video_more"><i class="fas fa-plus"></i></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tab 7: Additional Features -->
                        <div class="tab-pane fade" id="p7" role="tabpanel" aria-labelledby="p7_tab">
                            <h4 class="heading-in-tab">Existing Additional Features</h4>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            @* TODO: Display existing additional features when model is implemented *@
                                            <tr>
                                                <td colspan="3">No existing additional features</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <h4 class="heading-in-tab mt_30">New Additional Features</h4>
                            <div class="additional_feature_item">
                                <div class="row">
                                    <div class="col-md-5">
                                        <div class="form-group">
                                            <input type="text" name="AdditionalFeatureName[]" class="form-control" placeholder="Name">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <input type="text" name="AdditionalFeatureValue[]" class="form-control" placeholder="Value">
                                        </div>
                                    </div>
                                    <div class="col-md-1">
                                        <div class="btn btn-success add_additional_feature_more"><i class="fas fa-plus"></i></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tab 8: SEO -->
                        <div class="tab-pane fade" id="p8" role="tabpanel" aria-labelledby="p8_tab">
                            <div class="form-group">
                                <label for="">Title</label>
                                <input type="text" name="SeoTitle" class="form-control" value="@(ViewBag.Listing?.SeoTitle ?? "")">
                            </div>
                            <div class="form-group">
                                <label for="">Meta Description</label>
                                <textarea name="SeoMetaDescription" class="form-control h_100" cols="30" rows="10">@(ViewBag.Listing?.SeoMetaDescription ?? "")</textarea>
                            </div>
                        </div>

                        <!-- Tab 9: Status and Featured -->
                        <div class="tab-pane fade" id="p9" role="tabpanel" aria-labelledby="p9_tab">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="">Status</label>
                                        <select name="ListingStatus" class="form-control">
                                            <option value="Active" selected="@(ViewBag.Listing?.ListingStatus == "Active")">Active</option>
                                            <option value="Pending" selected="@(ViewBag.Listing?.ListingStatus == "Pending")">Pending</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="">Is Featured?</label>
                                        <select name="IsFeatured" class="form-control">
                                            <option value="Yes" selected="@(ViewBag.Listing?.IsFeatured == "Yes")">Yes</option>
                                            <option value="No" selected="@(ViewBag.Listing?.IsFeatured == "No")">No</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <button type="submit" class="btn btn-success btn-block mb_40">Update</button>
</form>

@* TODO: Add JavaScript for dynamic form elements and existing data management *@
