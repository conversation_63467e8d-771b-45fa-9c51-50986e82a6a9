@using CarPointCMS.Models.ViewModels
@{
    ViewData["Title"] = "Edit About Page Info";
}

@model PageAboutEditViewModel

<h1 class="h3 mb-3 text-gray-800">Edit About Page Info</h1>

<form asp-area="Admin" asp-controller="Pages" asp-action="About" method="post" enctype="multipart/form-data">
    <input asp-for="ExistingBanner" type="hidden">
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="form-group">
                <label asp-for="Name">Name</label>
                <input asp-for="Name" class="form-control">
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Detail">Detail</label>
                <textarea asp-for="Detail" class="form-control editor" cols="30" rows="10"></textarea>
                <span asp-validation-for="Detail" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label>Existing Banner</label>
                <div>
                    @if (!string.IsNullOrEmpty(Model.ExistingBanner))
                    {
                        <img src="~/uploads/page_banners/@Model.ExistingBanner" alt="" class="w_300">
                    }
                    else
                    {
                        <img src="~/uploads/page_banners/default.jpg" alt="" class="w_300">
                    }
                </div>
            </div>
            <div class="form-group">
                <label asp-for="Banner">Change Banner</label>
                <div>
                    <input asp-for="Banner" type="file">
                    <span asp-validation-for="Banner" class="text-danger"></span>
                </div>
            </div>
            <div class="form-group">
                <label asp-for="Status">Status</label>
                <div>
                    <div class="form-check form-check-inline">
                        <input asp-for="Status" class="form-check-input" type="radio" id="rr1" value="Show">
                        <label class="form-check-label font-weight-normal" for="rr1">Show</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input asp-for="Status" class="form-check-input" type="radio" id="rr2" value="Hide">
                        <label class="form-check-label font-weight-normal" for="rr2">Hide</label>
                    </div>
                </div>
                <span asp-validation-for="Status" class="text-danger"></span>
            </div>
        </div>
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">SEO Information</h6>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label asp-for="SeoTitle">Title</label>
                <input asp-for="SeoTitle" class="form-control">
                <span asp-validation-for="SeoTitle" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="SeoMetaDescription">Meta Description</label>
                <textarea asp-for="SeoMetaDescription" class="form-control h_100" cols="30" rows="10"></textarea>
                <span asp-validation-for="SeoMetaDescription" class="text-danger"></span>
            </div>
            <button type="submit" class="btn btn-success">Update</button>
        </div>
    </div>
</form>
