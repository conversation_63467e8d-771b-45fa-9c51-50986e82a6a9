@using CarPointCMS.Models.ViewModels
@{
    ViewData["Title"] = "Edit Email Template";
}

@model EmailTemplateEditViewModel

<h1 class="h3 mb-3 text-gray-800">Edit Email Template</h1>

<form asp-area="Admin" asp-controller="EmailTemplate" asp-action="Edit" asp-route-id="@Model.Id" method="post">
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 mt-2 font-weight-bold text-primary"></h6>
            <div class="float-right d-inline">
                <a asp-area="Admin" asp-controller="EmailTemplate" asp-action="Index" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> View All</a>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <label for="">Subject *</label>
                        <input type="text" name="EtSubject" class="form-control" value="@Model.EtSubject" autofocus>
                    </div>
                    <div class="form-group">
                        <label for="">Content *</label>
                        <textarea name="EtContent" class="form-control editor" cols="30" rows="10">@Model.EtContent</textarea>

                        <div class="font-weight-bold mt_20 text-danger">Parameters You Can Use:</div>

                        @if (Model.Id == 1)
                        {
                            <div>[[visitor_name]] = Visitor Name</div>
                            <div>[[visitor_email]] = Visitor Email</div>
                            <div>[[visitor_phone]] = Visitor Phone</div>
                            <div>[[visitor_message]] = Visitor Message</div>
                        }
                        else if (Model.Id == 2)
                        {
                            <div>[[person_name]] = Commenter Name</div>
                            <div>[[person_email]] = Commenter Email</div>
                            <div>[[person_comment]] = Commenter Message</div>
                            <div>[[comment_see_url]] = Admin Panel Link to See Comment</div>
                        }
                        else if (Model.Id == 5)
                        {
                            <div>[[reset_link]] = Reset Password Page Link</div>
                        }
                        else if (ViewBag.Id == 6)
                        {
                            <div>[[verification_link]] = Customer Registration Verify Link</div>
                        }
                        else if (ViewBag.Id == 7)
                        {
                            <div>[[reset_link]] = Reset Password Page Link</div>
                        }
                        else if (ViewBag.Id == 8)
                        {
                            <div>[[customer_name]] = Customer Name</div>
                            <div>[[payment_method]] = Payment Method</div>
                            <div>[[package_start_date]] = Package Start Date</div>
                            <div>[[package_end_date]] = Package End Date</div>
                            <div>[[transaction_id]] = Transaction ID</div>
                            <div>[[paid_amount]] = Paid Amount</div>
                            <div>[[payment_status]] = Payment Status</div>
                        }
                        else if (ViewBag.Id == 9)
                        {
                            <div>[[agent_name]] = Agent Name</div>
                            <div>[[listing_name]] = Listing Name</div>
                            <div>[[listing_url]] = Listing URL</div>
                            <div>[[name]] = Visitor Name</div>
                            <div>[[email]] = Visitor Email</div>
                            <div>[[phone]] = Visitor Phone</div>
                            <div>[[message]] = Visitor Message</div>
                        }
                        else if (ViewBag.Id == 10)
                        {
                            <div>[[listing_name]] = Listing Name</div>
                            <div>[[listing_url]] = Listing URL</div>
                            <div>[[name]] = Visitor Name</div>
                            <div>[[email]] = Visitor Email</div>
                            <div>[[phone]] = Visitor Phone</div>
                            <div>[[message]] = Visitor Message</div>
                        }

                    </div>
                </div>
            </div>
            <button type="submit" class="btn btn-success">Update</button>
        </div>
    </div>
</form>
