@using CarPointCMS.Models.ViewModels
@{
    ViewData["Title"] = "Add Currency";
}

@model CurrencyCreateViewModel

<h1 class="h3 mb-3 text-gray-800">Add Currency</h1>

<div class="row">
    <div class="col-md-6">
        <form asp-area="Admin" asp-controller="Currency" asp-action="Create" method="post">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 mt-2 font-weight-bold text-primary">Add Currency</h6>
                    <div class="float-right d-inline">
                        <a asp-area="Admin" asp-controller="Currency" asp-action="Index" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> View All</a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label asp-for="Name">Name *</label>
                        <input asp-for="Name" class="form-control" autofocus>
                        <span asp-validation-for="Name" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Symbol">Symbol *</label>
                        <input asp-for="Symbol" class="form-control">
                        <span asp-validation-for="Symbol" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Value">Value *</label>
                        <input asp-for="Value" class="form-control" type="number" step="0.01" min="0.01">
                        <span asp-validation-for="Value" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="IsDefault">Is Default *</label>
                        <select asp-for="IsDefault" class="form-control">
                            <option value="false">No</option>
                            <option value="true">Yes</option>
                        </select>
                        <span asp-validation-for="IsDefault" class="text-danger"></span>
                    </div>
                    <button type="submit" class="btn btn-success">Submit</button>
                </div>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
