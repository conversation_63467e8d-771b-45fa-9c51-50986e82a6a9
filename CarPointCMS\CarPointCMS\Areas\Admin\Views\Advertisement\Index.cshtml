@using CarPointCMS.Models.ViewModels
@{
    ViewData["Title"] = "Home Advertisements";
}

@model AdvertisementEditViewModel

<h1 class="h3 mb-3 text-gray-800">Home Advertisements</h1>

<form asp-area="Admin" asp-controller="Advertisement" asp-action="Index" method="post" enctype="multipart/form-data">
    <input type="hidden" name="CurrentAboveBrand1" value="@(Model.AboveBrand1 ?? "")">
    <input type="hidden" name="CurrentAboveBrand2" value="@(Model.AboveBrand2 ?? "")">
    <input type="hidden" name="CurrentAboveFeaturedListing1" value="@(Model.AboveFeaturedListing1 ?? "")">
    <input type="hidden" name="CurrentAboveFeaturedListing2" value="@(Model.AboveFeaturedListing2 ?? "")">
    <input type="hidden" name="CurrentAboveLocation1" value="@(Model.AboveLocation1 ?? "")">
    <input type="hidden" name="CurrentAboveLocation2" value="@(Model.AboveLocation2 ?? "")">

    <div class="card shadow mb-4 t-left">
        <div class="card-body">
            <div class="row">
                <div class="col-4 mb_30">
                    <div class="form-group">
                        <label for="">Above Brand 1</label>
                        <div>
                            <img src="~/uploads/advertisements/@(Model.AboveBrand1 ?? "default.jpg")" alt="">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="">Change Photo</label>
                        <div>
                            <input type="file" name="AboveBrand1">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="">URL</label>
                        <input type="text" name="AboveBrand1Url" class="form-control" value="@(Model.AboveBrand1Url ?? "")">"
                    </div>
                </div>
                <div class="col-4 mb_30">
                    <div class="form-group">
                        <label for="">Above Brand 2</label>
                        <div>
                            <img src="~/uploads/advertisements/@(Model.AboveBrand2 ?? "default.jpg")" alt="">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="">Change Photo</label>
                        <div>
                            <input type="file" name="AboveBrand2">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="">URL</label>
                        <input type="text" name="AboveBrand2Url" class="form-control" value="@(Model.AboveBrand2Url ?? "")">"
                    </div>
                </div>
                <div class="col-4 mb_30">
                    <div class="form-group">
                        <label for="">Above Brand Status</label>
                        <div>
                            <select name="AboveBrandStatus" class="form-control">
                                <option value="Show" selected="@(Model.AboveBrandStatus == Common.PageStatus.Show)">Show</option>
                                <option value="Hide" selected="@(Model.AboveBrandStatus == Common.PageStatus.Hide)">Hide</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="card shadow mb-4 t-left">
        <div class="card-body">
            <div class="row">
                <div class="col-4 mb_30">
                    <div class="form-group">
                        <label for="">Above Featured Listing 1</label>
                        <div>
                            <img src="~/uploads/advertisements/@(Model.AboveFeaturedListing1 ?? "default.jpg")" alt="">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="">Change Photo</label>
                        <div>
                            <input type="file" name="AboveFeaturedListing1">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="">URL</label>
                        <input type="text" name="AboveFeaturedListing1Url" class="form-control" value="@(Model.AboveFeaturedListing1Url ?? "")">"
                    </div>
                </div>
                <div class="col-4 mb_30">
                    <div class="form-group">
                        <label for="">Above Featured Listing 2</label>
                        <div>
                            <img src="~/uploads/advertisements/@(Model.AboveFeaturedListing2 ?? "default.jpg")" alt="">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="">Change Photo</label>
                        <div>
                            <input type="file" name="AboveFeaturedListing2">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="">URL</label>
                        <input type="text" name="AboveFeaturedListing2Url" class="form-control" value="@(Model.AboveFeaturedListing2Url ?? "")">"
                    </div>
                </div>
                <div class="col-4 mb_30">
                    <div class="form-group">
                        <label for="">Above Featured Listing Status</label>
                        <div>
                            <select name="AboveFeaturedListingStatus" class="form-control">
                                <option value="Show" selected="@(Model.AboveFeaturedListingStatus == Common.PageStatus.Show)">Show</option>
                                <option value="Hide" selected="@(Model.AboveFeaturedListingStatus == Common.PageStatus.Hide)">Hide</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="card shadow mb-4 t-left">
        <div class="card-body">
            <div class="row">
                <div class="col-4 mb_30">
                    <div class="form-group">
                        <label for="">Above Location 1</label>
                        <div>
                            <img src="~/uploads/advertisements/@(Model.AboveLocation1 ?? "default.jpg")" alt="">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="">Change Photo</label>
                        <div>
                            <input type="file" name="AboveLocation1">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="">URL</label>
                        <input type="text" name="AboveLocation1Url" class="form-control" value="@(Model.AboveLocation1Url ?? "")">"
                    </div>
                </div>
                <div class="col-4 mb_30">
                    <div class="form-group">
                        <label for="">Above Location 2</label>
                        <div>
                            <img src="~/uploads/advertisements/@(Model.AboveLocation2 ?? "default.jpg")" alt="">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="">Change Photo</label>
                        <div>
                            <input type="file" name="AboveLocation2">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="">URL</label>
                        <input type="text" name="AboveLocation2Url" class="form-control" value="@(Model.AboveLocation2Url ?? "")">"
                    </div>
                </div>
                <div class="col-4 mb_30">
                    <div class="form-group">
                        <label for="">Above Location Status</label>
                        <div>
                            <select name="AboveLocationStatus" class="form-control">
                                <option value="Show" selected="@(Model.AboveLocationStatus == Common.PageStatus.Show)">Show</option>
                                <option value="Hide" selected="@(Model.AboveLocationStatus == Common.PageStatus.Hide)">Hide</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <button type="submit" class="btn btn-success btn-block mb_50">Update</button>

</form>
