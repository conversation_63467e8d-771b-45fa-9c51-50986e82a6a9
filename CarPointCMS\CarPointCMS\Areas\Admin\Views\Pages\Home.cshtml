@using CarPointCMS.Models.ViewModels
@{
    ViewData["Title"] = "Edit Home Page Info";
}

@model PageHomeEditViewModel

<h1 class="h3 mb-3 text-gray-800">Edit Home Page Info</h1>

<form asp-area="Admin" asp-controller="Pages" asp-action="Home" method="post" enctype="multipart/form-data">
    <input type="hidden" name="CurrentSearchBackground" value="@(Model.ExistingSearchBackground ?? "")">
    <input type="hidden" name="CurrentTestimonialBackground" value="@(Model.ExistingTestimonialBackground ?? "")">
    <input type="hidden" name="CurrentVideoBackground" value="@(Model.ExistingVideoBackground ?? "")">

    <div class="card shadow mb-4 t-left">
        <div class="card-body">
            <div class="row">
                <div class="col-md-3 col-sm-12">
                    <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                        <a class="nav-link active" id="p1_tab" data-toggle="pill" href="#p1" role="tab" aria-controls="p1" aria-selected="true">SEO Section</a>
                        <a class="nav-link" id="p2_tab" data-toggle="pill" href="#p2" role="tab" aria-controls="p2" aria-selected="false">Search Section</a>
                        <a class="nav-link" id="p3_tab" data-toggle="pill" href="#p3" role="tab" aria-controls="p3" aria-selected="false">Brand Section</a>
                        <a class="nav-link" id="p6_tab" data-toggle="pill" href="#p6" role="tab" aria-controls="p6" aria-selected="false">Video Section</a>
                        <a class="nav-link" id="p4_tab" data-toggle="pill" href="#p4" role="tab" aria-controls="p4" aria-selected="false">Listing Section</a>
                        <a class="nav-link" id="p7_tab" data-toggle="pill" href="#p7" role="tab" aria-controls="p7" aria-selected="false">Testimonial Section</a>
                        <a class="nav-link" id="p5_tab" data-toggle="pill" href="#p5" role="tab" aria-controls="p5" role="tab" aria-controls="p5" aria-selected="false">Location Section</a>
                    </div>
                </div>
                <div class="col-md-9 col-sm-12">
                    <div class="tab-content" id="v-pills-tabContent">

                        <div class="tab-pane fade show active" id="p1" role="tabpanel" aria-labelledby="p1_tab">
                            <!-- SEO Section -->
                            <div class="form-group">
                                <label for="">Title</label>
                                <input type="text" name="SeoTitle" class="form-control" value="@(Model.SeoTitle ?? "")">"
                            </div>
                            <div class="form-group">
                                <label for="">Meta Description</label>
                                <textarea name="SeoMetaDescription" class="form-control h_70" cols="30" rows="10">@(Model.SeoMetaDescription ?? "")</textarea>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="p2" role="tabpanel" aria-labelledby="p2_tab">
                            <!-- Search Section -->
                            <div class="form-group">
                                <label for="">Search Heading</label>
                                <textarea name="SearchHeading" class="form-control h_70" cols="30" rows="10">@(Model.SearchHeading ?? "")</textarea>
                            </div>
                            <div class="form-group">
                                <label for="">Search Text</label>
                                <textarea name="SearchText" class="form-control h_70" cols="30" rows="10">@(Model.SearchText ?? "")</textarea>
                            </div>
                            <div class="form-group">
                                <label for="">Existing Search Background</label>
                                <div>
                                    <img src="~/uploads/site_photos/@(Model.ExistingSearchBackground ?? "default.jpg")" alt="" class="w_200">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="">Change Search Background</label>
                                <div>
                                    <input type="file" name="SearchBackground">
                                </div>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="p3" role="tabpanel" aria-labelledby="p3_tab">
                            <!-- Brand Section -->
                            <div class="form-group">
                                <label for="">Heading</label>
                                <input type="text" name="BrandHeading" class="form-control" value="@(Model.BrandHeading ?? "")">
                            </div>
                            <div class="form-group">
                                <label for="">Subheading</label>
                                <input type="text" name="BrandSubheading" class="form-control" value="@(Model.BrandSubheading ?? "")">
                            </div>
                            <div class="form-group">
                                <label for="">Total Items</label>
                                <input type="text" name="BrandTotal" class="form-control" value="@Model.BrandTotal">
                            </div>
                            <div class="form-group">
                                <label for="">Status</label>
                                <select name="BrandStatus" class="form-control">
                                    <option value="Show" selected="@(Model.BrandStatus == Common.PageStatus.Show)">Show</option>
                                    <option value="Hide" selected="@(Model.BrandStatus == Common.PageStatus.Hide)">Hide</option>
                                </select>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="p6" role="tabpanel" aria-labelledby="p6_tab">
                            <!-- Video Section -->
                            <div class="form-group">
                                <label for="">Video Heading</label>
                                <input type="text" name="VideoHeading" class="form-control" value="@(Model.VideoHeading ?? "")">
                            </div>
                            <div class="form-group">
                                <label for="">Video Text</label>
                                <textarea name="VideoText" class="form-control h_70" cols="30" rows="10">@(Model.VideoText ?? "")</textarea>
                            </div>
                            <div class="form-group">
                                <label for="">Video YouTube ID</label>
                                <input type="text" name="VideoYoutubeId" class="form-control" value="@(Model.VideoYoutubeId ?? "")">
                            </div>
                            <div class="form-group">
                                <label for="">Existing Video Background</label>
                                <div>
                                    <img src="~/uploads/site_photos/@(Model.ExistingVideoBackground ?? "default.jpg")" alt="" class="w_200">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="">Change Video Background</label>
                                <div>
                                    <input type="file" name="VideoBackground">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="">Status</label>
                                <select name="VideoStatus" class="form-control">
                                    <option value="Show" selected="@(Model.VideoStatus == Common.PageStatus.Show)">Show</option>
                                    <option value="Hide" selected="@(Model.VideoStatus == Common.PageStatus.Hide)">Hide</option>
                                </select>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="p4" role="tabpanel" aria-labelledby="p4_tab">
                            <!-- Listing Section -->
                            <div class="form-group">
                                <label for="">Heading</label>
                                <input type="text" name="ListingHeading" class="form-control" value="@(Model.ListingHeading ?? "")">
                            </div>
                            <div class="form-group">
                                <label for="">Subheading</label>
                                <input type="text" name="ListingSubheading" class="form-control" value="@(Model.ListingSubheading ?? "")">
                            </div>
                            <div class="form-group">
                                <label for="">Total Items</label>
                                <input type="text" name="ListingTotal" class="form-control" value="@Model.ListingTotal">
                            </div>
                            <div class="form-group">
                                <label for="">Status</label>
                                <select name="ListingStatus" class="form-control">
                                    <option value="Show" selected="@(Model.ListingStatus == Common.PageStatus.Show)">Show</option>
                                    <option value="Hide" selected="@(Model.ListingStatus == Common.PageStatus.Hide)">Hide</option>
                                </select>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="p5" role="tabpanel" aria-labelledby="p5_tab">
                            <!-- Location Section -->
                            <div class="form-group">
                                <label for="">Heading</label>
                                <input type="text" name="LocationHeading" class="form-control" value="@(Model.LocationHeading ?? "")">
                            </div>
                            <div class="form-group">
                                <label for="">Subheading</label>
                                <input type="text" name="LocationSubheading" class="form-control" value="@(Model.LocationSubheading ?? "")">
                            </div>
                            <div class="form-group">
                                <label for="">Total Items</label>
                                <input type="text" name="LocationTotal" class="form-control" value="@Model.LocationTotal">
                            </div>
                            <div class="form-group">
                                <label for="">Status</label>
                                <select name="LocationStatus" class="form-control">
                                    <option value="Show" selected="@(Model.LocationStatus == Common.PageStatus.Show)">Show</option>
                                    <option value="Hide" selected="@(Model.LocationStatus == Common.PageStatus.Hide)">Hide</option>
                                </select>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="p7" role="tabpanel" aria-labelledby="p7_tab">
                            <!-- Testimonial Section -->
                            <div class="form-group">
                                <label for="">Heading</label>
                                <input type="text" name="TestimonialHeading" class="form-control" value="@(Model.TestimonialHeading ?? "")">
                            </div>
                            <div class="form-group">
                                <label for="">Subheading</label>
                                <input type="text" name="TestimonialSubheading" class="form-control" value="@(Model.TestimonialSubheading ?? "")">
                            </div>
                            <div class="form-group">
                                <label for="">Existing Photo</label>
                                <div>
                                    <img src="~/uploads/site_photos/@(Model.ExistingTestimonialBackground ?? "default.jpg")" alt="" class="w_200">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="">Change Photo</label>
                                <div>
                                    <input type="file" name="TestimonialBackground">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="">Status</label>
                                <select name="TestimonialStatus" class="form-control">
                                    <option value="Show" selected="@(Model.TestimonialStatus == Common.PageStatus.Show)">Show</option>
                                    <option value="Hide" selected="@(Model.TestimonialStatus == Common.PageStatus.Hide)">Hide</option>
                                </select>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <button type="submit" class="btn btn-success btn-block mb_50">Update</button>
</form>
