@using CarPointCMS.Models.Entities
@{
    ViewData["Title"] = "Currency";
}

@model IEnumerable<Currency>

<h1 class="h3 mb-3 text-gray-800">Currency</h1>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 mt-2 font-weight-bold text-primary"></h6>
        <div class="float-right d-inline">
            <a asp-area="Admin" asp-controller="Currency" asp-action="Create" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> Add New</a>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="" width="100%" cellspacing="0">
                <thead>
                <tr>
                    <th>Serial</th>
                    <th>Name</th>
                    <th>Symbol</th>
                    <th>Value</th>
                    <th>Is Default</th>
                    <th>Action</th>
                </tr>
                </thead>
                <tbody>
                    @if (Model != null)
                    {
                        int i = 1;
                        @foreach (var row in Model)
                        {
                            <tr>
                                <td>@i</td>
                                <td>@row.Name</td>
                                <td>@row.Symbol</td>
                                <td>@row.Value</td>
                                <td>@(row.IsDefault ? "Yes" : "No")</td>
                                <td>
                                    <a asp-area="Admin" asp-controller="Currency" asp-action="Edit" asp-route-id="@row.Id" class="btn btn-warning btn-sm"><i class="fas fa-edit"></i></a>
                                    @if (row.Id != 1)
                                    {
                                        <form asp-area="Admin" asp-controller="Currency" asp-action="Delete" asp-route-id="@row.Id" method="post" style="display: inline;">
                                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure?');"><i class="fas fa-trash-alt"></i></button>
                                        </form>
                                    }
                                </td>
                            </tr>
                            i++;
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>
