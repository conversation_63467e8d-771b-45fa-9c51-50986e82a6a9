@using CarPointCMS.Models.ViewModels
@{
    ViewData["Title"] = "Edit Post";
}

@model BlogEditViewModel

<h1 class="h3 mb-3 text-gray-800">Edit Post</h1>

<form asp-area="Admin" asp-controller="Blog" asp-action="Edit" asp-route-id="@ViewBag.Id" method="post" enctype="multipart/form-data">
    <input asp-for="ExistingPhoto" type="hidden">

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 mt-2 font-weight-bold text-primary"></h6>
            <div class="float-right d-inline">
                <a asp-area="Admin" asp-controller="Blog" asp-action="Index" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> View All</a>
            </div>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label asp-for="PostTitle">Title *</label>
                <input asp-for="PostTitle" class="form-control" autofocus>
                <span asp-validation-for="PostTitle" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="PostSlug">Slug</label>
                <input asp-for="PostSlug" class="form-control">
                <span asp-validation-for="PostSlug" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="PostContent">Content *</label>
                <textarea asp-for="PostContent" class="form-control editor" cols="30" rows="10"></textarea>
                <span asp-validation-for="PostContent" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="PostContentShort">Short Content *</label>
                <textarea asp-for="PostContentShort" class="form-control h_100" cols="30" rows="10"></textarea>
                <span asp-validation-for="PostContentShort" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label>Existing Photo</label>
                <div>
                    @if (!string.IsNullOrEmpty(Model.ExistingPhoto))
                    {
                        <img src="~/uploads/post_photos/@Model.ExistingPhoto" alt="" class="w_200">
                    }
                    else
                    {
                        <img src="~/uploads/post_photos/default.jpg" alt="" class="w_200">
                    }
                </div>
            </div>
            <div class="form-group">
                <label asp-for="PostPhoto">Change Photo</label>
                <div>
                    <input asp-for="PostPhoto" type="file">
                    <span asp-validation-for="PostPhoto" class="text-danger"></span>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label asp-for="CategoryId">Select Category *</label>
                        <select asp-for="CategoryId" asp-items="Model.Categories" class="form-control">
                            <option value="">Select Category</option>
                        </select>
                        <span asp-validation-for="CategoryId" class="text-danger"></span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label asp-for="CommentShow">Show Comment *</label>
                        <select asp-for="CommentShow" class="form-control">
                            <option value="Yes">Yes</option>
                            <option value="No">No</option>
                        </select>
                        <span asp-validation-for="CommentShow" class="text-danger"></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">SEO Information</h6>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label asp-for="SeoTitle">Title</label>
                <input asp-for="SeoTitle" class="form-control">
                <span asp-validation-for="SeoTitle" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="SeoMetaDescription">Meta Description</label>
                <textarea asp-for="SeoMetaDescription" class="form-control h_100" cols="30" rows="10"></textarea>
                <span asp-validation-for="SeoMetaDescription" class="text-danger"></span>
            </div>
            <button type="submit" class="btn btn-success">Update</button>
        </div>
    </div>
</form>
