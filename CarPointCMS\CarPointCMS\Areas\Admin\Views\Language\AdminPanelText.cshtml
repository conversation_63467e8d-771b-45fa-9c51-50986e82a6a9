@using CarPointCMS.Models.ViewModels
@{
    ViewData["Title"] = "Language Admin Panel Text";
}

@model LanguageTextViewModel

<h1 class="h3 mb-3 text-gray-800">Language Admin Panel Text</h1>

<form asp-area="Admin" asp-controller="Language" asp-action="AdminPanelText" method="post">
    <div class="row">
        <div class="col-md-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 mt-2 font-weight-bold text-primary">Setup Key Values</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="" width="100%" cellspacing="0">
                            <thead>
                            <tr>
                                <th>Key</th>
                                <th>Value</th>
                            </tr>
                            </thead>
                            <tbody>
                                @if (Model.LanguageData != null && Model.LanguageData.Any())
                                {
                                    int i = 0;
                                    @foreach (var item in Model.LanguageData)
                                    {
                                        <input type="hidden" class="form-control" name="Keys[@i]" value="@item.Key">
                                        <tr>
                                            <td>
                                                <input type="text" name="" class="form-control" value="@item.Key" disabled>
                                            </td>
                                            <td>
                                                <input type="text" name="Values[@i]" class="form-control" value="@item.Value">
                                            </td>
                                        </tr>
                                        i++;
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="2" class="text-center">No language data available</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <button type="submit" class="btn btn-success btn-block">Update</button>
                </div>
            </div>
        </div>
    </div>
</form>
