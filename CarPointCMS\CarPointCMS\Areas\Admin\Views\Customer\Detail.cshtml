@using CarPointCMS.Models.ViewModels
@{
    ViewData["Title"] = "Customer Detail";
}

@model CustomerDetailViewModel

<h1 class="h3 mb-3 text-gray-800">Customer Detail</h1>

<div class="row">
    <div class="col-md-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 mt-2 font-weight-bold text-primary"></h6>
                <div class="float-right d-inline">
                    <a asp-area="Admin" asp-controller="Customer" asp-action="Index" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> Back to Previous</a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <tr>
                            <td>Photo</td>
                            <td>
                                @if (string.IsNullOrEmpty(Model.Customer.Photo))
                                {
                                    <img src="~/uploads/user_photos/default_photo.jpg" class="w_100">
                                }
                                else
                                {
                                    <img src="~/uploads/user_photos/@Model.Customer.Photo" class="w_100">
                                }
                            </td>
                        </tr>
                        <tr>
                            <td>Banner</td>
                            <td>
                                @if (string.IsNullOrEmpty(Model.Customer.Banner))
                                {
                                    <img src="~/uploads/user_photos/default_banner.jpg" class="w_200">
                                }
                                else
                                {
                                    <img src="~/uploads/user_photos/@Model.Customer.Banner" class="w_100">
                                }
                            </td>
                        </tr>
                        <tr>
                            <td>Name</td>
                            <td>@(Model.Customer.Name ?? "")</td>
                        </tr>
                        <tr>
                            <td>Email</td>
                            <td>@(Model.Customer.Email ?? "")</td>
                        </tr>
                        <tr>
                            <td>Phone</td>
                            <td>@(Model.Customer.Phone ?? "")</td>
                        </tr>
                        <tr>
                            <td>Country</td>
                            <td>@(Model.Customer.Country ?? "")</td>
                        </tr>
                        <tr>
                            <td>Address</td>
                            <td>@(Model.Customer.Address ?? "")</td>
                        </tr>
                        <tr>
                            <td>State</td>
                            <td>@(Model.Customer.State ?? "")</td>
                        </tr>
                        <tr>
                            <td>City</td>
                            <td>@(Model.Customer.City ?? "")</td>
                        </tr>
                        <tr>
                            <td>ZIP</td>
                            <td>@(Model.Customer.Zip ?? "")</td>
                        </tr>
                        <tr>
                            <td>Website</td>
                            <td>@(Model.Customer.Website ?? "")</td>
                        </tr>
                        <tr>
                            <td>Facebook</td>
                            <td>@(Model.Customer.Facebook ?? "")</td>
                        </tr>
                        <tr>
                            <td>Twitter</td>
                            <td>@(Model.Customer.Twitter ?? "")</td>
                        </tr>
                        <tr>
                            <td>LinkedIn</td>
                            <td>@(ViewBag.Customer?.LinkedIn ?? "")</td>
                        </tr>
                        <tr>
                            <td>Instagram</td>
                            <td>@(ViewBag.Customer?.Instagram ?? "")</td>
                        </tr>
                        <tr>
                            <td>Pinterest</td>
                            <td>@(ViewBag.Customer?.Pinterest ?? "")</td>
                        </tr>
                        <tr>
                            <td>YouTube</td>
                            <td>@(ViewBag.Customer?.YouTube ?? "")</td>
                        </tr>
                        <tr>
                            <td>Status</td>
                            <td>@(ViewBag.Customer?.Status ?? "")</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
